import httpx

class ChatwootService:
    def __init__(self, api_url: str, api_access_token: str):
        self.api_url = api_url
        self.api_access_token = api_access_token
        self.headers = {
            'api_access_token': self.api_access_token,
            'Content-Type': 'application/json'
        }

    async def send_message(self, conversation_id: int, content: str):
        async with httpx.AsyncClient() as client:
            url = f"{self.api_url}/conversations/{conversation_id}/messages"
            payload = {"content": content}
            response = await client.post(url, json=payload, headers=self.headers)
            return response.json()
