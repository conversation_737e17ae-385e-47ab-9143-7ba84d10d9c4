
from fastapi import FastAPI
from app.api import __init__ as api_init
from app.api.tour import router as tour_router
from app.api.chatwoot import router as chatwoot_router
from app.api.ai import router as ai_router

app = FastAPI(title="Vietravel AI Chatbot")

app.include_router(api_init.router)
app.include_router(tour_router)
app.include_router(chatwoot_router)
app.include_router(ai_router)

@app.get("/")
def root():
    return {"message": "Vietravel AI Chatbot is running."}
