from fastapi import APIRouter, HTTPException
from app.models.tour import Tour
from app.services.qdrant_service import QdrantService

router = APIRouter(prefix="/tours", tags=["Tours"])
qdrant_service = QdrantService()

@router.post("/add")
def add_tour(tour: Tour):
    qdrant_service.upsert_tour(tour)
    return {"message": "Tour added successfully"}

@router.get("/search")
def search_tours(query: str):
    results = qdrant_service.search_tours(query)
    return {"results": results}
