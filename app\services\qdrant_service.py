from qdrant_client import QdrantClient
from sentence_transformers import SentenceTransformer
from app.models.tour import Tour

class QdrantService:
    def __init__(self, host='localhost', port=6333):
        self.client = QdrantClient(host=host, port=port)
        self.model = SentenceTransformer('all-MiniLM-L6-v2')
        self.collection_name = 'tours'

    def upsert_tour(self, tour: Tour):
        vector = self.model.encode(tour.description)
        payload = tour.dict()
        self.client.upsert(collection_name=self.collection_name,
                           points=[{'id': tour.id, 'vector': vector, 'payload': payload}])

    def search_tours(self, query: str, limit: int = 5):
        query_vector = self.model.encode(query)
        results = self.client.search(collection_name=self.collection_name,
                                    query_vector=query_vector, limit=limit)
        return results
