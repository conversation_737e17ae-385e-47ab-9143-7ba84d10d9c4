✅ Mục tiêu:
 • Viết bot trade tự động bằng Python, chạy 24/7 trên VPS.
 • Áp dụng phương pháp trade T0 theo điều kiện kỹ thuật tùy chỉnh, hoạt động trên Binance Futures.
 • Tự động theo dõi biểu đồ, khi đủ điều kiện sẽ vào lệnh và đặt TP/SL kèm theo.

⸻

✅ 1. Kết nối API Binance:
 • Sử dụng Binance Futures API (USDT-M)
 • Kết nối bằng API Key + Secret do người dùng cung cấp
 • Chỉ bật quyền: Giao dịch (Trade) – KHÔNG bật Withdraw

⸻

✅ 2. Cặp giao dịch và khung thời gian:
 • Cặp mặc định: BTCUSDT
 • Áp dụng cho cả ETHUSDT sau này
 • Khung thời gian:
 • M15 → xác định xu hướng
 • M5 → tìm điểm vào lệnh
 • H1 → tham kh<PERSON>o xu hướng lớn (nếu cần)

⸻

✅ 3. Điều kiện vào lệnh – LONG

➤ Xác định xu hướng:
 • EMA20 > EMA50, cả 2 đang hướng lên
 • Giá nằm trên EMA100 và VWAP
 • RSI(14) nằm trong vùng từ 45–70

➤ Điều kiện vào lệnh:
 • Giá pullback về EMA20 hoặc VWAP
 • Có nến xác nhận xanh:
 • Close > 2/3 chiều dài nến
 • Râu nến ngắn
 • Volume của nến xác nhận ≥ 1.3 × Volume MA20
 • Không vào nếu RSI > 70

⸻

✅ 4. Điều kiện vào lệnh – SHORT

➤ Xác định xu hướng:
 • EMA9 < EMA21 < EMA100
 • Giá dưới VWAP
 • RSI(14) nằm trong vùng 30–55

➤ Điều kiện vào lệnh:
 • Giá bật xuống từ EMA9, EMA21 hoặc VWAP
 • Có nến đỏ xác nhận:
 • Close < 1/3 chiều dài nến
 • Râu trên ngắn
 • Volume nến xác nhận ≥ 1.3 × Volume MA20
 • Không vào nếu RSI < 30
 • Cho phép linh hoạt nếu volume không đủ mạnh nhưng mẫu nến mạnh và đúng vùng kỹ thuật

⸻

✅ 5. Quản lý lệnh:
 • SL (Stop Loss):
 • Dưới đáy gần nhất (Long) / Trên đỉnh gần nhất (Short)
 • Hoặc: SL = Entry ± 1.5 × ATR14
 • TP (Take Profit):
 • TP1: +0.7% → Dời SL về Entry
 • TP2: ****% đến ****%
 • Tối thiểu R:R = 1:2

⸻

✅ 6. Quản lý bot & cảnh báo:
 • Ghi log mỗi lệnh vào file hoặc gửi về Telegram (tùy chọn)
 • Cảnh báo khi vào lệnh: Entry – SL – TP
 • Kiểm tra bot còn hoạt động không mỗi 15 phút

⸻

✅ 7. VPS & chạy liên tục:
 • Bot sẽ được triển khai trên VPS (Linux Ubuntu)
 • Chạy 24/7 qua tmux hoặc supervisor
 • Nếu lỗi kết nối API → Retry sau 10s, nếu lỗi quá 3 lần → cảnh báo

⸻

✅ 8. Tùy biến:
 • Có thể điều chỉnh thông số EMA, RSI, ATR qua file cấu hình JSON/YAML
 • Sau này có thể mở rộng đa cặp, đa timeframe

⸻

✅ Yêu cầu đầu ra:
 • 1 file Python chạy trên VPS
 • Hướng dẫn cài đặt môi trường (requirements.txt)
 • Có thể bật/tắt bot dễ dàng
 • Option gửi log qua Telegram càng tốt
 Nhưng điều kiện mình có thể thay đổi tuỳ theo chiến lược. Nó có các chỉ báo sẵn rồi mình sear theo thông số tuỳ theo điều kiện thị trường