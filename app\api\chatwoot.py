from fastapi import APIRouter, Request
from app.services.chatwoot_service import ChatwootService

router = APIRouter(prefix="/chatwoot", tags=["Chatwoot"])
chatwoot_service = ChatwootService(api_url="https://chatwoot.example.com/api/v1", api_access_token="YOUR_TOKEN")

@router.post("/webhook")
async def chatwoot_webhook(request: Request):
    data = await request.json()
    # Xử lý dữ liệu webhook từ Chatwoot
    return {"status": "received", "data": data}
